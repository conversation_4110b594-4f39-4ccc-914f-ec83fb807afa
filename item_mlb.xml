<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="140dp"
    android:layout_marginStart="50dp"
    android:layout_marginTop="6dp"
    android:layout_marginEnd="50dp"
    android:layout_marginBottom="6dp"
    android:background="@drawable/selector_nba"
    android:focusable="true"
    android:orientation="horizontal"
    tools:background="@drawable/shape_bg_gradient">

    <ImageView
        android:id="@+id/iv_away_team_logo"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="20dp"
        android:focusable="false"
        tools:src="@drawable/espn_nba" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="16dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="16dp"
        android:layout_weight="1"
        android:duplicateParentState="true"
        android:focusable="false"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:ellipsize="marquee"
            android:focusable="false"
            android:gravity="center_horizontal"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:textColor="@color/selector_color_main"
            android:textSize="18sp"
            tools:text="Arizona vs Los Chi. White Sox" />

        <TextView
            android:id="@+id/tv_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:duplicateParentState="true"
            android:focusable="false"
            android:gravity="center_horizontal"
            android:textColor="#FF0F00"
            android:textSize="20sp"
            tools:text="CHW 7 - ARI 3" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="4dp"
            android:duplicateParentState="true"
            android:focusable="false"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:duplicateParentState="true"
                android:focusable="false"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_option1"
                    style="@style/font_bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:duplicateParentState="true"
                    android:focusable="false"
                    android:textColor="@color/selector_color_main"
                    android:textSize="14sp"
                    tools:text="PITCHER (W)" />

                <TextView
                    android:id="@+id/tv_content_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:duplicateParentState="true"
                    android:focusable="false"
                    android:textColor="@color/selector_color_main"
                    android:textSize="12sp"
                    tools:text="J. Misiorowski" />

                <TextView
                    android:id="@+id/tv_description1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:duplicateParentState="true"
                    android:focusable="false"
                    android:textColor="@color/selector_color_main"
                    android:textSize="10sp"
                    tools:text="(4-7)" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:duplicateParentState="true"
                android:focusable="false"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_option2"
                    style="@style/font_bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:duplicateParentState="true"
                    android:focusable="false"
                    android:textColor="@color/selector_color_main"
                    android:textSize="14sp"
                    tools:text="PITCHER (L)" />

                <TextView
                    android:id="@+id/tv_content_2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:duplicateParentState="true"
                    android:focusable="false"
                    android:textColor="@color/selector_color_main"
                    android:textSize="12sp"
                    tools:text="J. Misiorowski" />

                <TextView
                    android:id="@+id/tv_description2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:duplicateParentState="true"
                    android:focusable="false"
                    android:textColor="@color/selector_color_main"
                    android:textSize="10sp"
                    tools:text="(4-7)" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_save_pitcher"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:duplicateParentState="true"
                android:focusable="false"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/tv_option3"
                    style="@style/font_bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:duplicateParentState="true"
                    android:focusable="false"
                    android:textColor="@color/selector_color_main"
                    android:textSize="14sp"
                    tools:text="PITCHER (S)" />

                <TextView
                    android:id="@+id/tv_content_3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:duplicateParentState="true"
                    android:focusable="false"
                    android:textColor="@color/selector_color_main"
                    android:textSize="12sp"
                    tools:text="J. Misiorowski" />

                <TextView
                    android:id="@+id/tv_description3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:duplicateParentState="true"
                    android:focusable="false"
                    android:textColor="@color/selector_color_main"
                    android:textSize="10sp"
                    tools:text="(4-7)" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_home_team_logo"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_gravity="center_vertical"
        android:focusable="false"
        tools:src="@drawable/espn_nba" />

    <View
        android:layout_width="0.5dp"
        android:layout_height="match_parent"
        android:layout_marginStart="60dp"
        android:layout_marginTop="28dp"
        android:layout_marginBottom="28dp"
        android:background="@color/selector_color_main"
        android:focusable="false" />

    <androidx.leanback.widget.VerticalGridView
        android:id="@+id/rv_channels"
        android:layout_width="80dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="12dp"
        android:descendantFocusability="blocksDescendants"
        android:duplicateParentState="true"
        android:focusable="false"
        android:focusableInTouchMode="false" />

    <TextView
        android:id="@+id/tv_date"
        android:layout_width="80dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:focusable="false"
        android:gravity="center"
        android:textColor="@color/selector_color_main"
        tools:text="20:20 PM" />

</LinearLayout>