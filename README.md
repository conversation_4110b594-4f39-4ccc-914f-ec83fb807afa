# MLB EPG 焦点展开/收缩功能实现

## 功能概述

实现了MLB EPG列表项的焦点响应式展开/收缩功能，满足以下需求：

1. **焦点状态管理**：获得焦点时展开，失去焦点时收缩
2. **展开状态**：显示PITCHER区域（树形结构），台标区域显示台号
3. **收缩状态**：隐藏PITCHER区域，台标区域只保留图片并横向展示

## 实现文件

### 核心文件
- `MLBEpgPresenter.kt` - 主要的Presenter类，处理焦点逻辑和状态切换
- `item_mlb.xml` - MLB项目的布局文件，添加了PITCHER区域ID
- `ChannelAdapter.kt` - 频道适配器，支持展开/收缩两种显示模式

### 布局文件
- `item_channel_expanded.xml` - 展开状态的频道项布局（显示图标+台号+频道名）
- `item_channel_collapsed.xml` - 收缩状态的频道项布局（只显示图标）

### 数据类
- `MLBGame.kt` - MLB游戏数据模型
- `Channel.kt` - 频道数据模型（在ChannelAdapter.kt中定义）

### 工具类
- `BasePresenter.kt` - 基础Presenter类
- `DBApi.kt` - 数据库API模拟类
- `DateUtil.kt` - 日期工具类
- `R.kt` - 资源ID定义类

### 演示文件
- `MLBEpgDemo.kt` - 演示如何使用该功能的Activity

## 功能特性

### 1. 焦点状态管理
- 继承自`BasePresenter`，重写`onCreateViewHolder`方法
- 在焦点变化时自动调用展开/收缩方法
- 保持原有的焦点样式处理逻辑

### 2. 展开状态 (setExpandedState)
- **PITCHER区域**：设置`ll_pitcher_area`可见性为`true`
- **台标区域**：
  - 使用垂直布局的`LinearLayoutManager`
  - 显示完整频道信息（图标+台号+频道名）
  - 支持显示所有频道
- **布局高度**：调整为200dp

### 3. 收缩状态 (setCollapsedState)
- **PITCHER区域**：设置`ll_pitcher_area`可见性为`false`
- **台标区域**：
  - 使用横向布局的`LinearLayoutManager`
  - 只显示频道图标
  - 最多显示3个频道
- **布局高度**：保持140dp

### 4. 频道适配器 (ChannelAdapter)
- 支持两种显示模式：展开和收缩
- **展开模式**：使用`item_channel_expanded.xml`，显示图标、台号、频道名
- **收缩模式**：使用`item_channel_collapsed.xml`，只显示图标
- 使用Glide加载频道图标

## 使用方法

### 1. 基本使用
```kotlin
val presenter = MLBEpgPresenter()
val adapter = ArrayObjectAdapter(presenter)
verticalGridView.adapter = adapter

// 添加数据
val game = MLBGame(
    id = "1",
    awayTeamCity = "Arizona",
    awayTeam = "Diamondbacks",
    homeTeamCity = "Chicago", 
    homeTeam = "White Sox",
    channelNumbers = "101,102,103"
)
adapter.add(game)
```

### 2. 自定义配置
```kotlin
// 修改展开/收缩高度
companion object {
    private const val EXPANDED_HEIGHT = 250  // 自定义展开高度
    private const val COLLAPSED_HEIGHT = 120 // 自定义收缩高度
}
```

## 技术实现要点

### 1. 焦点处理
- 重写`BasePresenter.onCreateViewHolder()`方法
- 在原有焦点逻辑基础上添加展开/收缩功能
- 保持文本样式和选中状态的处理

### 2. 布局动态调整
- 通过修改`layoutParams.height`动态调整项目高度
- 使用`isVisible`属性控制PITCHER区域显示/隐藏
- 动态切换RecyclerView的LayoutManager方向

### 3. 适配器模式
- `ChannelAdapter`根据`isExpanded`参数选择不同布局
- 使用`getItemViewType()`区分展开/收缩状态
- 在`ViewHolder`中根据状态显示不同内容

### 4. 数据绑定
- 支持多种游戏状态：Final(4)、Live(2/6)、Pre-Game(1/5)
- 根据游戏状态显示不同的PITCHER信息
- 频道信息从`channelNumbers`字符串解析

## 扩展性

### 1. 添加新的展示状态
可以在`setExpandedState`和`setCollapsedState`方法中添加更多UI元素的控制

### 2. 自定义动画
可以在状态切换时添加动画效果：
```kotlin
// 添加高度变化动画
val animator = ValueAnimator.ofInt(currentHeight, targetHeight)
animator.addUpdateListener { animation ->
    layoutParams.height = animation.animatedValue as Int
    view.layoutParams = layoutParams
}
animator.start()
```

### 3. 更多频道显示模式
可以扩展`ChannelAdapter`支持更多显示模式，如网格布局等

## 注意事项

1. **性能优化**：频繁的焦点切换可能影响性能，建议添加防抖处理
2. **内存管理**：确保Glide图片加载的生命周期管理
3. **兼容性**：测试不同屏幕尺寸下的显示效果
4. **数据验证**：添加对`channelNumbers`等数据的空值检查

## 测试建议

1. 测试快速焦点切换的稳定性
2. 测试不同数据状态下的显示效果
3. 测试内存使用情况
4. 测试在不同设备上的兼容性
