# MLB EPG 焦点展开/收缩功能实现

## 功能概述

实现了MLB EPG列表项的焦点响应式展开/收缩功能，带有平滑动画效果和智能防抖机制：

1. **焦点状态管理**：获得焦点时平滑展开，失去焦点时平滑收缩
2. **展开状态**：显示PITCHER区域（树形结构），台标区域显示台号
3. **收缩状态**：隐藏PITCHER区域，台标区域只保留图片并横向展示
4. **动画效果**：300ms平滑过渡动画，避免生硬的状态切换
5. **防抖机制**：快速移动时不触发动画，停留时才执行

## 实现文件

### 核心文件
- `MLBEpgPresenter.kt` - 主要的Presenter类，处理焦点逻辑和状态切换
- `item_mlb.xml` - MLB项目的布局文件，添加了PITCHER区域ID
- `ChannelAdapter.kt` - 频道适配器，支持展开/收缩两种显示模式

### 布局文件
- `item_channel_expanded.xml` - 展开状态的频道项布局（显示图标+台号+频道名）
- `item_channel_collapsed.xml` - 收缩状态的频道项布局（只显示图标）

### 数据类
- `MLBGame.kt` - MLB游戏数据模型
- `Channel.kt` - 频道数据模型（在ChannelAdapter.kt中定义）

### 工具类
- `BasePresenter.kt` - 基础Presenter类
- `DBApi.kt` - 数据库API模拟类
- `DateUtil.kt` - 日期工具类
- `R.kt` - 资源ID定义类

### 演示文件
- `MLBEpgDemo.kt` - 演示如何使用该功能的Activity

## 功能特性

### 1. 焦点状态管理（带防抖）
- 继承自`BasePresenter`，重写`onCreateViewHolder`方法
- **获得焦点**：延迟150ms执行展开动画，快速移动时会取消
- **失去焦点**：立即执行收缩动画，确保响应性
- 保持原有的焦点样式处理逻辑

### 2. 展开动画 (setExpandedStateWithAnimation)
- **高度动画**：从当前高度平滑增长到200dp（300ms）
- **PITCHER区域**：淡入显示（延迟100ms开始，避免突兀）
- **台标区域**：
  - 切换为垂直布局的`LinearLayoutManager`
  - 显示完整频道信息（图标+台号+频道名）
  - 支持显示所有频道
- **插值器**：使用`AccelerateDecelerateInterpolator`

### 3. 收缩动画 (setCollapsedStateWithAnimation)
- **PITCHER区域**：快速淡出（150ms），动画结束后隐藏
- **高度动画**：平滑收缩到140dp（延迟75ms开始）
- **台标区域**：
  - 切换为横向布局的`LinearLayoutManager`
  - 只显示频道图标
  - 最多显示3个频道
- **优化**：收缩动画更快，提升响应性

### 4. 频道适配器 (ChannelAdapter)
- 支持两种显示模式：展开和收缩
- **展开模式**：使用`item_channel_expanded.xml`，显示图标、台号、频道名
- **收缩模式**：使用`item_channel_collapsed.xml`，只显示图标
- 使用Glide加载频道图标

## 使用方法

### 1. 基本使用
```kotlin
val presenter = MLBEpgPresenter()
val adapter = ArrayObjectAdapter(presenter)
verticalGridView.adapter = adapter

// 添加数据
val game = MLBGame(
    id = "1",
    awayTeamCity = "Arizona",
    awayTeam = "Diamondbacks",
    homeTeamCity = "Chicago", 
    homeTeam = "White Sox",
    channelNumbers = "101,102,103"
)
adapter.add(game)
```

### 2. 自定义配置
```kotlin
// 修改展开/收缩高度和动画参数
companion object {
    private const val EXPANDED_HEIGHT = 250      // 自定义展开高度
    private const val COLLAPSED_HEIGHT = 120     // 自定义收缩高度
    private const val ANIMATION_DURATION = 400L  // 动画持续时间
    private const val FOCUS_DELAY = 200L         // 焦点停留延迟时间
}
```

### 3. 资源清理
```kotlin
// 在Activity或Fragment的onDestroy中调用
override fun onDestroy() {
    super.onDestroy()
    presenter.cleanup() // 清理动画资源
}
```

## 技术实现要点

### 1. 防抖机制
- 使用`Handler`和`Runnable`实现延迟执行
- **获得焦点**：150ms延迟，快速移动时自动取消
- **失去焦点**：立即执行，确保响应性
- 维护`pendingAnimations`映射表管理待执行任务

### 2. 动画系统
- 使用`AnimatorSet`组合多个动画
- **高度动画**：`ValueAnimator`实现平滑高度变化
- **透明度动画**：`ObjectAnimator`实现淡入淡出效果
- **时序控制**：通过`startDelay`控制动画执行顺序

### 3. 资源管理
- 维护`currentAnimators`映射表跟踪正在执行的动画
- 自动取消冲突的动画，避免状态混乱
- 提供`cleanup()`方法清理资源，防止内存泄漏

### 4. 布局动态调整
- 通过修改`layoutParams.height`动态调整项目高度
- 使用`isVisible`和`alpha`属性控制PITCHER区域显示
- 动态切换RecyclerView的LayoutManager方向

### 5. 适配器模式
- `ChannelAdapter`根据`isExpanded`参数选择不同布局
- 使用`getItemViewType()`区分展开/收缩状态
- 在`ViewHolder`中根据状态显示不同内容

### 6. 数据绑定
- 支持多种游戏状态：Final(4)、Live(2/6)、Pre-Game(1/5)
- 根据游戏状态显示不同的PITCHER信息
- 频道信息从`channelNumbers`字符串解析

## 扩展性

### 1. 添加新的展示状态
可以在`setExpandedState`和`setCollapsedState`方法中添加更多UI元素的控制

### 2. 自定义动画
可以在状态切换时添加动画效果：
```kotlin
// 添加高度变化动画
val animator = ValueAnimator.ofInt(currentHeight, targetHeight)
animator.addUpdateListener { animation ->
    layoutParams.height = animation.animatedValue as Int
    view.layoutParams = layoutParams
}
animator.start()
```

### 3. 更多频道显示模式
可以扩展`ChannelAdapter`支持更多显示模式，如网格布局等

## 注意事项

1. **性能优化**：已实现防抖处理，避免频繁动画影响性能
2. **内存管理**：必须调用`cleanup()`方法清理动画资源
3. **动画冲突**：系统自动取消冲突动画，确保状态一致性
4. **兼容性**：测试不同屏幕尺寸下的显示效果
5. **数据验证**：添加对`channelNumbers`等数据的空值检查

## 测试建议

1. **快速焦点切换**：测试快速上下移动时动画的稳定性和防抖效果
2. **动画流畅性**：测试展开/收缩动画的流畅度和时序
3. **内存使用**：长时间使用后检查内存泄漏情况
4. **不同数据状态**：测试各种游戏状态下的显示效果
5. **设备兼容性**：测试在不同屏幕尺寸和性能设备上的表现
6. **资源清理**：测试Activity/Fragment销毁时的资源清理

## 动画参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `EXPANDED_HEIGHT` | 200dp | 展开状态的高度 |
| `COLLAPSED_HEIGHT` | 140dp | 收缩状态的高度 |
| `ANIMATION_DURATION` | 300ms | 动画持续时间 |
| `FOCUS_DELAY` | 150ms | 焦点停留延迟时间 |

## 性能特性

- ✅ **防抖机制**：避免快速移动时的无效动画
- ✅ **动画复用**：减少对象创建和内存分配
- ✅ **智能取消**：自动取消冲突的动画
- ✅ **资源清理**：防止内存泄漏
- ✅ **流畅过渡**：300ms平滑动画，用户体验佳
