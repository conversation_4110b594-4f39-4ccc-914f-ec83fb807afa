package com.huishine.traveler.page.event.mlb

import android.app.Activity
import android.os.Bundle
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.VerticalGridView
import com.huishine.traveler.R
import com.huishine.traveler.entity.MLBGame

/**
 * MLB EPG演示Activity
 * 展示焦点展开/收缩功能
 */
class MLBEpgDemo : Activity() {

    private lateinit var verticalGridView: VerticalGridView
    private lateinit var adapter: ArrayObjectAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setupViews()
        setupData()
    }

    private fun setupViews() {
        // 创建VerticalGridView
        verticalGridView = VerticalGridView(this)
        setContentView(verticalGridView)
        
        // 设置适配器
        adapter = ArrayObjectAdapter(MLBEpgPresenter())
        verticalGridView.adapter = adapter
    }

    private fun setupData() {
        // 创建示例数据
        val games = listOf(
            MLBGame(
                id = "1",
                awayTeamCity = "Arizona",
                awayTeam = "Diamondbacks",
                homeTeamCity = "Chicago",
                homeTeam = "White Sox",
                gameStatus = "4", // Final
                channelNumbers = "101,102,103",
                winnerFirstName = "John",
                winnerLastName = "Doe",
                winnerWins = "10",
                winnerLosses = "5",
                loserFirstName = "Jane",
                loserLastName = "Smith",
                loserWins = "8",
                loserLosses = "7"
            ),
            MLBGame(
                id = "2",
                awayTeamCity = "New York",
                awayTeam = "Yankees",
                homeTeamCity = "Boston",
                homeTeam = "Red Sox",
                gameStatus = "2", // Live
                channelNumbers = "104,105",
                inningDivision = "Top",
                inning = "7th",
                homeStarterFirstName = "Mike",
                homeStarterLastName = "Johnson",
                homeStarterDesc = "RHP",
                awayStarterFirstName = "Tom",
                awayStarterLastName = "Wilson",
                awayStarterDesc = "LHP"
            ),
            MLBGame(
                id = "3",
                awayTeamCity = "Los Angeles",
                awayTeam = "Dodgers",
                homeTeamCity = "San Francisco",
                homeTeam = "Giants",
                gameStatus = "1", // Pre-Game
                channelNumbers = "106,107,108,109",
                arenaName = "Oracle Park",
                homeStarterFirstName = "Chris",
                homeStarterLastName = "Brown",
                homeStarterDesc = "RHP",
                awayStarterFirstName = "Alex",
                awayStarterLastName = "Davis",
                awayStarterDesc = "LHP",
                gameDateTimeEst = "2024-07-09T19:30:00"
            )
        )

        // 添加数据到适配器
        games.forEach { game ->
            adapter.add(game)
        }
    }
}

/**
 * 使用说明：
 * 
 * 1. 焦点状态管理：
 *    - 当item获得焦点时，会自动展开显示PITCHER区域
 *    - 当item失去焦点时，会自动收缩隐藏PITCHER区域
 * 
 * 2. 展开状态特性：
 *    - 显示PITCHER区域（包含投手信息的树形结构）
 *    - 台标区域显示完整的频道信息（图标+台号+频道名）
 *    - 布局高度增加到200dp
 * 
 * 3. 收缩状态特性：
 *    - 隐藏PITCHER区域
 *    - 台标区域只显示频道图标，横向排列
 *    - 布局高度保持140dp
 * 
 * 4. 台标区域展示：
 *    - 展开时：垂直布局，显示图标、台号、频道名
 *    - 收缩时：横向布局，只显示图标
 *    - 支持多个频道的展示
 */
