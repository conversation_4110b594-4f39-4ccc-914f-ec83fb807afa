package com.huishine.traveler.page.event.mlb

import android.app.Activity
import android.os.Bundle
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.VerticalGridView
import com.huishine.traveler.R
import com.huishine.traveler.entity.MLBGame

/**
 * MLB EPG演示Activity
 * 展示焦点展开/收缩功能
 */
class MLBEpgDemo : Activity() {

    private lateinit var verticalGridView: VerticalGridView
    private lateinit var adapter: ArrayObjectAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setupViews()
        setupData()
    }

    private fun setupViews() {
        // 创建VerticalGridView
        verticalGridView = VerticalGridView(this)
        setContentView(verticalGridView)

        // 创建自定义动画配置
        val animationConfig = AnimationConfigBuilder()
            .expandedHeight(220)
            .animationDuration(350L)
            .focusDelay(120L)
            .enableAnimation(true)
            .enableDebounce(true)
            .build()

        // 或者使用预设配置
        // val animationConfig = AnimationConfig.fast() // 快速动画
        // val animationConfig = AnimationConfig.slow() // 慢速动画
        // val animationConfig = AnimationConfig.immediate() // 无动画

        // 设置适配器
        adapter = ArrayObjectAdapter(MLBEpgPresenter(animationConfig))
        verticalGridView.adapter = adapter
    }

    private fun setupData() {
        // 创建示例数据
        val games = listOf(
            MLBGame(
                id = "1",
                awayTeamCity = "Arizona",
                awayTeam = "Diamondbacks",
                homeTeamCity = "Chicago",
                homeTeam = "White Sox",
                gameStatus = "4", // Final
                channelNumbers = "101,102,103",
                winnerFirstName = "John",
                winnerLastName = "Doe",
                winnerWins = "10",
                winnerLosses = "5",
                loserFirstName = "Jane",
                loserLastName = "Smith",
                loserWins = "8",
                loserLosses = "7"
            ),
            MLBGame(
                id = "2",
                awayTeamCity = "New York",
                awayTeam = "Yankees",
                homeTeamCity = "Boston",
                homeTeam = "Red Sox",
                gameStatus = "2", // Live
                channelNumbers = "104,105",
                inningDivision = "Top",
                inning = "7th",
                homeStarterFirstName = "Mike",
                homeStarterLastName = "Johnson",
                homeStarterDesc = "RHP",
                awayStarterFirstName = "Tom",
                awayStarterLastName = "Wilson",
                awayStarterDesc = "LHP"
            ),
            MLBGame(
                id = "3",
                awayTeamCity = "Los Angeles",
                awayTeam = "Dodgers",
                homeTeamCity = "San Francisco",
                homeTeam = "Giants",
                gameStatus = "1", // Pre-Game
                channelNumbers = "106,107,108,109",
                arenaName = "Oracle Park",
                homeStarterFirstName = "Chris",
                homeStarterLastName = "Brown",
                homeStarterDesc = "RHP",
                awayStarterFirstName = "Alex",
                awayStarterLastName = "Davis",
                awayStarterDesc = "LHP",
                gameDateTimeEst = "2024-07-09T19:30:00"
            )
        )

        // 添加数据到适配器
        games.forEach { game ->
            adapter.add(game)
        }
    }
}

    override fun onDestroy() {
        super.onDestroy()
        // 清理动画资源
        (adapter.presenterSelector as? MLBEpgPresenter)?.cleanup()
    }
}

/**
 * 使用说明：
 *
 * 1. 焦点状态管理（带动画效果）：
 *    - 当item获得焦点并停留150ms后，会平滑展开显示PITCHER区域
 *    - 当item失去焦点时，会立即开始平滑收缩动画
 *    - 快速上下移动时不会触发动画，避免性能问题
 *
 * 2. 展开动画特性：
 *    - 高度从140dp平滑增长到200dp（300ms动画）
 *    - PITCHER区域淡入显示（延迟100ms开始）
 *    - 台标区域切换为垂直布局，显示完整信息
 *    - 使用AccelerateDecelerateInterpolator插值器
 *
 * 3. 收缩动画特性：
 *    - PITCHER区域快速淡出（150ms）
 *    - 高度平滑收缩到140dp（延迟75ms开始）
 *    - 台标区域切换为横向布局，只显示图标
 *
 * 4. 防抖机制：
 *    - 获得焦点：延迟150ms执行，快速移动时会取消
 *    - 失去焦点：立即执行，确保响应性
 *    - 自动取消之前的动画，避免冲突
 *
 * 5. 性能优化：
 *    - 动画复用，避免重复创建
 *    - 资源清理，防止内存泄漏
 *    - 智能防抖，减少不必要的动画
 *
 * 6. 台标区域展示：
 *    - 展开时：垂直布局，显示图标、台号、频道名
 *    - 收缩时：横向布局，只显示图标（最多3个）
 *    - 支持多个频道的平滑切换
 */
