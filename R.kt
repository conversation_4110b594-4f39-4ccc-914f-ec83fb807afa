package com.huishine.traveler

/**
 * 资源ID类（简化版本）
 */
object R {
    object layout {
        const val item_mlb = 1001
        const val item_channel_expanded = 1002
        const val item_channel_collapsed = 1003
    }
    
    object id {
        const val tv_title = 2001
        const val tv_description = 2002
        const val iv_away_team_logo = 2003
        const val iv_home_team_logo = 2004
        const val rv_channels = 2005
        const val tv_date = 2006
        const val ll_pitcher_area = 2007
        const val tv_option1 = 2008
        const val tv_content_1 = 2009
        const val tv_description1 = 2010
        const val tv_option2 = 2011
        const val tv_content_2 = 2012
        const val tv_description2 = 2013
        const val tv_option3 = 2014
        const val tv_content_3 = 2015
        const val tv_description3 = 2016
        const val ll_save_pitcher = 2017
        const val iv_channel_logo = 2018
        const val tv_channel_number = 2019
        const val tv_channel_name = 2020
    }
    
    object mipmap {
        const val dif_ic_logo_default = 3001
    }
    
    object color {
        const val selector_color_main = 4001
    }
    
    object drawable {
        const val selector_nba = 5001
        const val shape_bg_gradient = 5002
        const val espn_nba = 5003
    }
    
    object style {
        const val font_bold = 6001
    }
}
