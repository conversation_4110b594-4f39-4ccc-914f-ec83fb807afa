<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="4dp"
    android:orientation="vertical"
    android:padding="4dp">

    <!-- 频道图标 -->
    <ImageView
        android:id="@+id/iv_channel_logo"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_gravity="center_horizontal"
        android:scaleType="centerCrop"
        tools:src="@drawable/espn_nba" />

    <!-- 台号 -->
    <TextView
        android:id="@+id/tv_channel_number"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="2dp"
        android:textColor="@color/selector_color_main"
        android:textSize="10sp"
        android:textStyle="bold"
        tools:text="101" />

    <!-- 频道名称 -->
    <TextView
        android:id="@+id/tv_channel_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="1dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/selector_color_main"
        android:textSize="8sp"
        tools:text="ESPN" />

</LinearLayout>
