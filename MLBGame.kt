package com.huishine.traveler.entity

/**
 * MLB游戏数据类
 */
data class MLBGame(
    val id: String? = null,
    val awayTeamCity: String? = null,
    val awayTeam: String? = null,
    val homeTeamCity: String? = null,
    val homeTeam: String? = null,
    val awayTeamLogo: String? = null,
    val homeTeamLogo: String? = null,
    val gameDate: String? = null,
    val gameDateTimeEst: String? = null,
    val gameStatus: String? = null,
    val channelNumbers: String? = null,
    val arenaName: String? = null,
    val inningDivision: String? = null,
    val inning: String? = null,
    val homeStarterFirstName: String? = null,
    val homeStarterLastName: String? = null,
    val homeStarterDesc: String? = null,
    val awayStarterFirstName: String? = null,
    val awayStarterLastName: String? = null,
    val awayStarterDesc: String? = null,
    val awayTeamTricode: String? = null,
    val awayTeamScore: String? = null,
    val homeTeamTricode: String? = null,
    val homeTeamScore: String? = null,
    val winnerFirstName: String? = null,
    val winnerLastName: String? = null,
    val winnerWins: String? = null,
    val winnerLosses: String? = null,
    val loserFirstName: String? = null,
    val loserLastName: String? = null,
    val loserWins: String? = null,
    val loserLosses: String? = null,
    val saverFirstName: String? = null,
    val saverLastName: String? = null,
    val saverWins: String? = null
)
