package com.huishine.traveler.page.event.mlb

import android.annotation.SuppressLint
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.huishine.traveler.R
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.huishine.traveler.base.BasePresenter
import com.huishine.traveler.database.DBApi
import com.huishine.traveler.entity.MLBGame
import com.huishine.traveler.page.event.ChannelAdapter
import android.widget.LinearLayout
import com.huishine.traveler.util.DateUtil
import android.view.ViewGroup
import androidx.leanback.widget.Presenter


@SuppressLint("SetTextI18n")
class MLBEpgPresenter() : BasePresenter<MLBGame>() {

    companion object {
        private const val EXPANDED_HEIGHT = 200
        private const val COLLAPSED_HEIGHT = 140
    }

    override fun layoutId(): Int {
        return R.layout.item_mlb
    }

    override fun addFocusTextStyle(): List<Int> {
        return emptyList()
    }

    override fun onKeyListener(
        v: View,
        keyCode: Int,
        event: KeyEvent
    ): Boolean {
        return false
    }

    override fun onCreateViewHolder(parent: ViewGroup): ViewHolder {
        val viewHolder = super.onCreateViewHolder(parent)

        // 添加我们的焦点处理逻辑
        viewHolder.view.setOnFocusChangeListener { view, hasFocus ->
            // 先调用父类的焦点处理逻辑
            map[hasFocus] = view.tag as MLBGame
            if (map[true] == map[false] && needKeep) {
                view.isActivated = !hasFocus
                lastSelectedView = view
            } else {
                lastSelectedView?.isActivated = false
            }

            view.isSelected = hasFocus

            addFocusTextStyle().forEach {
                setFocusTextStyle(view, it, hasFocus)
            }

            // 添加我们的展开/收缩逻辑
            val item = view.tag as? MLBGame
            if (item != null) {
                if (hasFocus) {
                    setExpandedState(view, item)
                } else {
                    setCollapsedState(view, item)
                }
            }
        }

        return viewHolder
    }

    private fun setFocusTextStyle(rootView: View, resId: Int, hasFocus: Boolean) {
        val textView = rootView.findViewById<TextView>(resId)
        textView?.typeface = if (hasFocus) android.graphics.Typeface.DEFAULT_BOLD else android.graphics.Typeface.DEFAULT
    }

    @SuppressLint("SetTextI18n")
    override fun bindViewHolder(view: View, item: MLBGame) {
        val tvTitle = view.findViewById<TextView>(R.id.tv_title)

        tvTitle.text =
            "${item.awayTeamCity} ${item.awayTeam} vs. ${item.homeTeamCity} ${item.homeTeam}"

        val awayLogo = item.awayTeamLogo
        val homeLogo = item.homeTeamLogo

        Glide.with(context)
            .load(awayLogo)
            .diskCacheStrategy(DiskCacheStrategy.NONE)
            .error(R.mipmap.dif_ic_logo_default)
            .into(view.findViewById(R.id.iv_away_team_logo))

        Glide.with(context)
            .load(homeLogo)
            .diskCacheStrategy(DiskCacheStrategy.NONE)
            .error(R.mipmap.dif_ic_logo_default)
            .into(view.findViewById(R.id.iv_home_team_logo))

        val game = if (DateUtil.isToday(parseToDate2(item.gameDate!!))) {
            DBApi.getInstance().getLatestMLBEvent(item.id!!)
        } else item

        when (game!!.gameStatus) {
            "4" -> previousGame(view, game) // 4, 名称=Final
            "2", "6" -> liveGame(view, game)//2, 名称=In-Progress 6, 名称=Suspended
            "1", "5" -> futureGame(view, game)  // 1, 名称=Pre-Game 5, 名称=Postponed
            else -> liveGame(view, game)
        }

        setupChannelsDisplay(view, item)

        // 初始状态设置为收缩状态
        setCollapsedState(view, item)
    }

    private fun setupChannelsDisplay(view: View, item: MLBGame) {
        val rvChannels = view.findViewById<RecyclerView>(R.id.rv_channels)
        rvChannels.setFocusable(false)
        rvChannels.setFocusableInTouchMode(false)

        if (item.channelNumbers.isNullOrEmpty()) {
            rvChannels.isVisible = false
        } else {
            val channelNumbers = item.channelNumbers!!.split(",")
            val ids = channelNumbers.mapNotNull { it.toIntOrNull() }.toIntArray()
            val channels = DBApi.getInstance().getChannelByChannelNumber(ids = ids)

            // 设置RecyclerView
            val adapter = ChannelAdapter(channels)
            rvChannels.layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            rvChannels.adapter = adapter
            rvChannels.isVisible = true
        }
    }



    private fun liveGame(view: View, item: MLBGame) {
        val tvEPGDescription = view.findViewById<TextView>(R.id.tv_description)

        tvEPGDescription.text = "${item.inningDivision ?: ""} ${item.inning ?: ""}"

        val tvOption1 = view.findViewById<TextView>(R.id.tv_option1)
        val tvContent1 = view.findViewById<TextView>(R.id.tv_content_1)
        val tvDescription1 = view.findViewById<TextView>(R.id.tv_description1)
        tvOption1.text = "HOME STARTER"
        tvContent1.text = "${item.homeStarterFirstName ?: ""} ${item.homeStarterLastName ?: ""}"
        tvDescription1.text = item.homeStarterDesc ?: ""

        val tvOption2 = view.findViewById<TextView>(R.id.tv_option2)
        val tvContent2 = view.findViewById<TextView>(R.id.tv_content_2)
        val tvDescription2 = view.findViewById<TextView>(R.id.tv_description2)
        tvOption2.text = "AWAY STARTER"
        tvContent2.text = "${item.awayStarterFirstName ?: ""} ${item.awayStarterLastName ?: ""}"
        tvDescription2.text = item.awayStarterDesc ?: ""

        val llSavePitcher = view.findViewById<View>(R.id.ll_save_pitcher)
        llSavePitcher.isVisible = false
        val tvOption3 = view.findViewById<TextView>(R.id.tv_option3)
        val tvContent3 = view.findViewById<TextView>(R.id.tv_content_3)
        val tvDescription3 = view.findViewById<TextView>(R.id.tv_description3)
        tvOption3.text = ""
        tvContent3.text = ""
        tvDescription3.text = ""

        val tvDate = view.findViewById<TextView>(R.id.tv_date)
        tvDate.text = if (item.gameStatus == "2")
            "LIVE"
        else
            "Suspended"
    }

    private fun futureGame(view: View, item: MLBGame) {
        val tvEPGDescription = view.findViewById<TextView>(R.id.tv_description)
        val description = "${item.arenaName}"
        tvEPGDescription.text = description

        val tvOption1 = view.findViewById<TextView>(R.id.tv_option1)
        val tvContent1 = view.findViewById<TextView>(R.id.tv_content_1)
        val tvDescription1 = view.findViewById<TextView>(R.id.tv_description1)
        tvOption1.text = "HOME STARTER"
        tvContent1.text = "${item.homeStarterFirstName ?: ""} ${item.homeStarterLastName ?: ""}"
        tvDescription1.text = item.homeStarterDesc ?: ""

        val tvOption2 = view.findViewById<TextView>(R.id.tv_option2)
        val tvContent2 = view.findViewById<TextView>(R.id.tv_content_2)
        val tvDescription2 = view.findViewById<TextView>(R.id.tv_description2)
        tvOption2.text = "AWAY STARTER"
        tvContent2.text = "${item.awayStarterFirstName ?: ""} ${item.awayStarterLastName ?: ""}"
        tvDescription2.text = item.awayStarterDesc ?: ""

        val llSavePitcher = view.findViewById<View>(R.id.ll_save_pitcher)
        llSavePitcher.isVisible = false
        val tvOption3 = view.findViewById<TextView>(R.id.tv_option3)
        val tvContent3 = view.findViewById<TextView>(R.id.tv_content_3)
        val tvDescription3 = view.findViewById<TextView>(R.id.tv_description3)
        tvOption3.text = ""
        tvContent3.text = ""
        tvDescription3.text = ""

        val tvDate = view.findViewById<TextView>(R.id.tv_date)
        tvDate.text = if (item.gameStatus == "5")
            "Postponed"
        else
            DateUtil.getFormatString(
                item.gameDateTimeEst!!,
                "hh:mm aa"
            )
    }

    private fun previousGame(view: View, item: MLBGame) {
        val tvEPGDescription = view.findViewById<TextView>(R.id.tv_description)
        val description =
            "${item.awayTeamTricode ?: ""}  ${item.awayTeamScore ?: ""} - ${item.homeTeamTricode ?: ""}  ${item.homeTeamScore ?: ""}"
        tvEPGDescription.text = description

        val tvOption1 = view.findViewById<TextView>(R.id.tv_option1)
        val tvContent1 = view.findViewById<TextView>(R.id.tv_content_1)
        val tvDescription1 = view.findViewById<TextView>(R.id.tv_description1)
        tvOption1.text = "PITCHER (W)"
        tvContent1.text = "${item.winnerFirstName ?: ""} ${item.winnerLastName ?: ""}"
        tvDescription1.text = "(${item.winnerWins ?: ""}-${item.winnerLosses ?: ""})"

        val tvOption2 = view.findViewById<TextView>(R.id.tv_option2)
        val tvContent2 = view.findViewById<TextView>(R.id.tv_content_2)
        val tvDescription2 = view.findViewById<TextView>(R.id.tv_description2)
        tvOption2.text = "PITCHER (L)"
        tvContent2.text = "${item.loserFirstName ?: ""} ${item.loserLastName ?: ""}"
        tvDescription2.text = "(${item.loserWins ?: ""}-${item.loserLosses ?: ""})"

        val tvOption3 = view.findViewById<TextView>(R.id.tv_option3)
        val tvContent3 = view.findViewById<TextView>(R.id.tv_content_3)
        val tvDescription3 = view.findViewById<TextView>(R.id.tv_description3)
        val llSavePitcher = view.findViewById<View>(R.id.ll_save_pitcher)
        if (!item.saverFirstName.isNullOrEmpty() && !item.saverFirstName.isNullOrEmpty()) {
            llSavePitcher.isVisible = true
            tvOption3.text = "PITCHER (S)"
            tvContent3.text = "${item.saverWins ?: ""} ${item.saverLastName ?: ""}"
            tvDescription3.text = "(${item.saverWins ?: ""})"
        } else {
            llSavePitcher.isVisible = false
            tvOption3.text = ""
            tvContent3.text = ""
            tvDescription3.text = ""
        }
        val tvDate = view.findViewById<TextView>(R.id.tv_date)
        tvDate.text = "PLAYBACK"
    }

    private fun setExpandedState(view: View, item: MLBGame) {
        // 展开状态：显示PITCHER区域，台标区域显示台号
        val pitcherArea = view.findViewById<LinearLayout>(R.id.ll_pitcher_area)
        val rvChannels = view.findViewById<RecyclerView>(R.id.rv_channels)

        // 显示PITCHER区域（树形结构）
        pitcherArea?.isVisible = true

        // 台标区域显示台号（垂直布局）
        if (!item.channelNumbers.isNullOrEmpty()) {
            val channelNumbers = item.channelNumbers!!.split(",")
            val ids = channelNumbers.mapNotNull { it.toIntOrNull() }.toIntArray()
            val channels = DBApi.getInstance().getChannelByChannelNumber(ids = ids)

            // 创建展开状态的适配器，显示台号
            val expandedAdapter = ChannelAdapter(channels, true) // true表示展开状态
            rvChannels.layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            rvChannels.adapter = expandedAdapter
            rvChannels.isVisible = true
        }

        // 调整布局高度
        val layoutParams = view.layoutParams
        layoutParams.height = EXPANDED_HEIGHT
        view.layoutParams = layoutParams
    }

    private fun setCollapsedState(view: View, item: MLBGame) {
        // 收缩状态：隐藏PITCHER区域，台标区域只保留图片并横向展示
        val pitcherArea = view.findViewById<LinearLayout>(R.id.ll_pitcher_area)
        val rvChannels = view.findViewById<RecyclerView>(R.id.rv_channels)

        // 隐藏PITCHER区域
        pitcherArea?.isVisible = false

        // 台标区域只保留图片并横向展示
        if (!item.channelNumbers.isNullOrEmpty()) {
            val channelNumbers = item.channelNumbers!!.split(",")
            val ids = channelNumbers.take(3).mapNotNull { it.toIntOrNull() }.toIntArray() // 只取前3个
            val channels = DBApi.getInstance().getChannelByChannelNumber(ids = ids)

            // 创建收缩状态的适配器，只显示图片
            val collapsedAdapter = ChannelAdapter(channels.take(3), false) // false表示收缩状态
            rvChannels.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
            rvChannels.adapter = collapsedAdapter
            rvChannels.isVisible = true
        }

        // 调整布局高度
        val layoutParams = view.layoutParams
        layoutParams.height = COLLAPSED_HEIGHT
        view.layoutParams = layoutParams
    }
}


