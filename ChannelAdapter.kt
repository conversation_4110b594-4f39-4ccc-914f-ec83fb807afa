package com.huishine.traveler.page.event

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.huishine.traveler.R

/**
 * 频道适配器，支持展开和收缩两种显示模式
 * @param channels 频道列表
 * @param isExpanded 是否为展开状态，true=展开（显示台号），false=收缩（只显示图片）
 */
class ChannelAdapter(
    private val channels: List<Channel>,
    private val isExpanded: Boolean = false
) : RecyclerView.Adapter<ChannelAdapter.ChannelViewHolder>() {

    companion object {
        private const val TYPE_EXPANDED = 1
        private const val TYPE_COLLAPSED = 2
    }

    override fun getItemViewType(position: Int): Int {
        return if (isExpanded) TYPE_EXPANDED else TYPE_COLLAPSED
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChannelViewHolder {
        val layoutId = if (viewType == TYPE_EXPANDED) {
            R.layout.item_channel_expanded
        } else {
            R.layout.item_channel_collapsed
        }
        
        val view = LayoutInflater.from(parent.context).inflate(layoutId, parent, false)
        return ChannelViewHolder(view, viewType == TYPE_EXPANDED)
    }

    override fun onBindViewHolder(holder: ChannelViewHolder, position: Int) {
        holder.bind(channels[position])
    }

    override fun getItemCount(): Int = channels.size

    class ChannelViewHolder(
        itemView: View,
        private val isExpanded: Boolean
    ) : RecyclerView.ViewHolder(itemView) {

        private val ivChannelLogo: ImageView = itemView.findViewById(R.id.iv_channel_logo)
        private val tvChannelNumber: TextView? = if (isExpanded) {
            itemView.findViewById(R.id.tv_channel_number)
        } else null
        private val tvChannelName: TextView? = if (isExpanded) {
            itemView.findViewById(R.id.tv_channel_name)
        } else null

        fun bind(channel: Channel) {
            // 加载频道图标
            Glide.with(itemView.context)
                .load(channel.logoUrl)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .error(R.mipmap.dif_ic_logo_default)
                .into(ivChannelLogo)

            if (isExpanded) {
                // 展开状态：显示台号和频道名称
                tvChannelNumber?.text = channel.channelNumber.toString()
                tvChannelName?.text = channel.channelName
                tvChannelNumber?.isVisible = true
                tvChannelName?.isVisible = true
            }
        }
    }
}

/**
 * 频道数据类
 */
data class Channel(
    val id: Int,
    val channelNumber: Int,
    val channelName: String,
    val logoUrl: String?
)
