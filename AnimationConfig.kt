package com.huishine.traveler.page.event.mlb

import android.view.animation.Interpolator
import android.view.animation.AccelerateDecelerateInterpolator

/**
 * 动画配置类
 * 允许用户自定义展开/收缩动画的各种参数
 */
data class AnimationConfig(
    /**
     * 展开状态的高度（dp）
     */
    val expandedHeight: Int = 200,
    
    /**
     * 收缩状态的高度（dp）
     */
    val collapsedHeight: Int = 140,
    
    /**
     * 动画持续时间（毫秒）
     */
    val animationDuration: Long = 300L,
    
    /**
     * 焦点停留延迟时间（毫秒）
     * 用户快速移动时，只有停留超过此时间才会触发展开动画
     */
    val focusDelay: Long = 150L,
    
    /**
     * 高度动画的插值器
     */
    val heightInterpolator: Interpolator = AccelerateDecelerateInterpolator(),
    
    /**
     * 透明度动画的插值器
     */
    val alphaInterpolator: Interpolator = AccelerateDecelerateInterpolator(),
    
    /**
     * PITCHER区域淡入动画的延迟时间（毫秒）
     * 相对于整个动画开始时间的延迟
     */
    val fadeInDelay: Long = animationDuration / 3,
    
    /**
     * PITCHER区域淡出动画的持续时间（毫秒）
     * 通常比整体动画更快
     */
    val fadeOutDuration: Long = animationDuration / 2,
    
    /**
     * 高度动画的延迟时间（毫秒）
     * 在收缩动画中，高度变化会稍微延迟开始
     */
    val heightAnimationDelay: Long = animationDuration / 4,
    
    /**
     * 是否启用动画
     * 设置为false时将使用立即切换
     */
    val animationEnabled: Boolean = true,
    
    /**
     * 是否启用防抖机制
     * 设置为false时将立即响应焦点变化
     */
    val debounceEnabled: Boolean = true
) {
    
    companion object {
        /**
         * 快速动画配置
         * 适用于性能较低的设备
         */
        fun fast() = AnimationConfig(
            animationDuration = 200L,
            focusDelay = 100L,
            fadeInDelay = 50L,
            fadeOutDuration = 100L,
            heightAnimationDelay = 50L
        )
        
        /**
         * 慢速动画配置
         * 适用于需要更明显动画效果的场景
         */
        fun slow() = AnimationConfig(
            animationDuration = 500L,
            focusDelay = 200L,
            fadeInDelay = 150L,
            fadeOutDuration = 200L,
            heightAnimationDelay = 100L
        )
        
        /**
         * 无动画配置
         * 立即切换状态，适用于性能敏感场景
         */
        fun immediate() = AnimationConfig(
            animationEnabled = false,
            debounceEnabled = false
        )
        
        /**
         * 高性能配置
         * 启用防抖但禁用动画
         */
        fun performant() = AnimationConfig(
            animationEnabled = false,
            debounceEnabled = true,
            focusDelay = 100L
        )
    }
    
    /**
     * 验证配置参数的有效性
     */
    fun validate(): AnimationConfig {
        require(expandedHeight > collapsedHeight) {
            "展开高度必须大于收缩高度"
        }
        require(animationDuration > 0) {
            "动画持续时间必须大于0"
        }
        require(focusDelay >= 0) {
            "焦点延迟时间不能为负数"
        }
        require(fadeInDelay >= 0) {
            "淡入延迟时间不能为负数"
        }
        require(fadeOutDuration > 0) {
            "淡出持续时间必须大于0"
        }
        require(heightAnimationDelay >= 0) {
            "高度动画延迟时间不能为负数"
        }
        
        return this
    }
}

/**
 * 动画配置构建器
 * 提供链式调用方式配置动画参数
 */
class AnimationConfigBuilder {
    private var config = AnimationConfig()
    
    fun expandedHeight(height: Int) = apply { 
        config = config.copy(expandedHeight = height) 
    }
    
    fun collapsedHeight(height: Int) = apply { 
        config = config.copy(collapsedHeight = height) 
    }
    
    fun animationDuration(duration: Long) = apply { 
        config = config.copy(animationDuration = duration) 
    }
    
    fun focusDelay(delay: Long) = apply { 
        config = config.copy(focusDelay = delay) 
    }
    
    fun heightInterpolator(interpolator: Interpolator) = apply { 
        config = config.copy(heightInterpolator = interpolator) 
    }
    
    fun alphaInterpolator(interpolator: Interpolator) = apply { 
        config = config.copy(alphaInterpolator = interpolator) 
    }
    
    fun enableAnimation(enabled: Boolean) = apply { 
        config = config.copy(animationEnabled = enabled) 
    }
    
    fun enableDebounce(enabled: Boolean) = apply { 
        config = config.copy(debounceEnabled = enabled) 
    }
    
    fun build(): AnimationConfig = config.validate()
}

/**
 * 使用示例：
 * 
 * // 使用预设配置
 * val fastConfig = AnimationConfig.fast()
 * val slowConfig = AnimationConfig.slow()
 * 
 * // 使用构建器自定义配置
 * val customConfig = AnimationConfigBuilder()
 *     .expandedHeight(250)
 *     .animationDuration(400L)
 *     .focusDelay(200L)
 *     .enableAnimation(true)
 *     .build()
 */
